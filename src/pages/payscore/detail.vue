<template>
  <div class="box">
    <blockquote v-if="loading">
      <div class="header">
        <div class="card" style="background-color: #eeeeee"></div>
      </div>
      <div class="buddy">
        <div class="line" v-for="i in 4" :key="i">
          <div class="label" style="color: #999999">健身卡</div>
          <div class="value" style="color: #999999">{{ i }}</div>
        </div>
      </div>
    </blockquote>
    <blockquote v-else>
      <div class="header">
        <div class="card">
          <img
            class="card-img"
            src="https://imagecdn.rocketbird.cn/minprogram/uni-member/payscore-card-bg.png"
            alt="card"
          />
          <div class="card-type">{{ cardType }}</div>
        </div>
      </div>
      <div class="buddy" v-if="signed">
        <div class="line" style="border-top: 1rpx solid #f6f6f8">
          <div class="label">{{ cardLabel }}</div>
          <div class="value">{{ info.member_card_name }}</div>
        </div>
        <div class="line">
          <div class="label">套餐信息</div>
          <div class="value">自动续费</div>
        </div>
        <div class="line">
          <div class="label">下次续费金额</div>
          <div class="value">¥{{ price }}</div>
        </div>
        <div class="line">
          <div class="label">下次续费日期</div>
          <div class="value">{{ deadline }}</div>
        </div>
      </div>
      <div class="buddy" v-else>
        <div class="line" style="border-top: 1rpx solid #f6f6f8">
          <div class="label">{{ cardLabel }}</div>
          <div class="value">{{ info.member_card_name }}</div>
        </div>
        <div class="line">
          <div class="label">门店名称</div>
          <div class="value">{{ info.shop_name }}</div>
        </div>
        <div class="line">
          <div class="label">连续包月</div>
          <div class="value">¥{{ info.dec_price }}</div>
        </div>
        <div class="line" @click="handleSubscribe">
          <div class="label">包月期限/计划</div>
          <div class="value" v-if="info.dec_term != -1">
            {{ info.dec_term }}期
            <div class="arrow"></div>
          </div>
          <div class="value" v-else>
            无期限
            <div class="arrow"></div>
          </div>
        </div>
        <div class="line">
          <div class="label">签约销售</div>
          <!-- <picker
            v-if="sellerList.length"
            @change="handleSellerChange"
            :value="sellerIndex"
            :range="sellerList"
            range-key="name"
          >
            <div class="value" style="width: 500rpx; height: 80rpx; justify-content: flex-end">
              {{ sellerList[sellerIndex].name }}
              <div class="arrow"></div>
            </div>
          </picker> -->
          <SaleListDrawer
            :list="sellerList"
            :selected-id="saleId"
            @change="handleSaleChangeByDrawer"
          />
        </div>
      </div>
      <div class="close-box" v-if="signed">
        <div class="normal-btn close-btn" @click="handleClose">关闭自动续费</div>
      </div>
      <div class="footer" v-else>
        <div class="protocol">
          <checkbox-group @change="checkboxChange">
            <label class="checkbox"> <checkbox value="checked" :checked="checked" /> 我同意 </label>
          </checkbox-group>
          <div class="link" @click="handleProtocol">《会员购买协议》</div>
        </div>
        <div class="sign">
          <div class="price">
            <div class="label">连续包月</div>
            <div class="unit">¥</div>
            <div class="value">{{ info.dec_price }}</div>
          </div>
          <div class="normal-btn sign-btn" @click="handleSign">立即购买</div>
        </div>
      </div>
    </blockquote>
  </div>
</template>

<script setup lang="ts" name="payscore-detail">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import { formatDate } from '@/utils/shared'
import SaleListDrawer from '../card/components/SaleListDrawer.vue'

// loading
const loading = ref(false)

const userStore = useUserStore()
const { checkLogin } = useLogin()

// check signed
const signed = ref(false)
const getSigned = () => {
  return http
    .post('/Contract/checkBusContractStatus', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      loading: false,
    })
    .then((res) => {
      signed.value = res?.data?.user_status
    })
}

// information
const id = ref('')
// 卡种类型(1时段卡，2次数卡，3储值卡,4私教卡 5泳教)
const cardType = ref('健身卡')
const cardLabel = ref('健身卡名称')
const info = ref({}) as any
const getInformation = () => {
  return http
    .post('/Contract/queryBusProgrammeInfo', {
      bus_id: userStore.userInfoBusId,
      programme_id: id.value,
      loading: false,
    })
    .then((res) => {
      info.value = res.data
      if (info.value.member_card_type == 1) {
        cardType.value = '期限卡'
        cardLabel.value = '会员卡名称'
      } else if (info.value.member_card_type == 2) {
        cardType.value = '次数卡'
        cardLabel.value = '会员卡名称'
      } else if (info.value.member_card_type == 3) {
        cardType.value = '储值卡'
        cardLabel.value = '储值卡名称'
      } else if (info.value.member_card_type == 4) {
        cardType.value = '私教卡'
        cardLabel.value = '课程名称'
      } else if (info.value.member_card_type == 5) {
        cardType.value = '泳教卡'
        cardLabel.value = '课程名称'
      }
    })
}

// deadline
const deadline = ref('')
const price = ref('')
const getDeadline = () => {
  return http
    .post('/Contract/queryUserContract', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      loading: false,
    })
    .then((res) => {
      let timestamp = res.data?.contract?.dec_time
      if (timestamp) {
        timestamp = timestamp * 1000
      } else {
        timestamp = new Date().getTime()
      }
      const date = new Date(timestamp)
      deadline.value = formatDate(date, 'yyyy-MM-dd')
      id.value = res.data?.programme_id
      price.value = res.data?.amount
    })
}

// subscribe
const handleSubscribe = () => {
  uni.navigateTo({
    url: `/pages/payscore/subscribe?id=${id.value}`,
  })
}

// protocol
const handleProtocol = () => {
  uni.navigateTo({
    url: '/pages/payscore/protocol',
  })
}

// seller
const sellerIndex = ref(0)
const sellerList = ref([]) as any
const getSellerList = () => {
  return http
    .post('/Contract/querySalePeopleList', {
      bus_id: userStore.userInfoBusId,
      shop_id: info.value.shop_id,
      loading: false,
    })
    .then((res) => {
      sellerList.value = [{ id: -1, name: '请选择销售人员' }, ...res.data]
    })
}
// const handleSellerChange = (e) => {
//   sellerIndex.value = Number(e.detail.value)
// }

// event checkbox
const checked = ref(false)
const checkboxChange = (e) => {
  checked.value = e.detail.value.includes('checked')
}
// event button
const handleSign = () => {
  if (!checked.value) {
    uni.showToast({
      title: '请勾选购买协议',
      image: '../../static/img/warning.png',
    })
    return false
  }
  if (signed.value) {
    uni.showToast({
      title: '您已与商家签约',
      image: '../../static/img/warning.png',
    })
    return false
  }

  let sellerId = ''
  if (sellerIndex.value) {
    sellerId = sellerList.value[sellerIndex.value].id
  } else {
    uni.showToast({
      title: '请选择销售人员',
      image: '../../static/img/warning.png',
    })
    return false
  }

  http
    .post('/Contract/applyContract', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      programme_id: id.value,
      saler_id: sellerId,
      loading: true,
    })
    .then((res) => {
      uni.navigateToMiniProgram({
        ...res.data,
        success(res) {
          console.log(res)
        },
        fail(res) {
          console.log(res)
        },
        complete(res) {
          console.log(res)
        },
      })
    })
}

const handleClose = () => {
  uni.showModal({
    title: '提示',
    content: '请确认是否关闭自动续费?关闭后将不在发起自动扣款申请',
    success: (res) => {
      if (res.confirm) {
        http
          .post('/Contract/delContract', {
            bus_id: userStore.userInfoBusId,
            user_id: userStore.userInfoUserId,
            loading: true,
          })
          .then((res) => {
            uni.showModal({
              title: '提示',
              content: `关闭成功！您的入场期限将于${deadline.value}到期`,
              showCancel: false,
              success: (res) => {
                uni.navigateBack()
              },
            })
          })
      }
    },
  })
}

const saleId = ref('')
const saleName = ref('')
const handleSaleChangeByDrawer = (data) => {
  saleId.value = data.id
  saleName.value = data.name
}

// life cycle
const busId = ref('')
onLoad((options) => {
  id.value = options.id || ''
  busId.value = options.bus_id || ''
  loading.value = true
})

onShow(async () => {
  await checkLogin(true, busId.value)
  await getSigned()
  if (signed.value) {
    await getDeadline() // get programme_id
    await getInformation() // need programme_id
  } else {
    await getInformation() // get shop_id
    await getSellerList() // need shop_id
  }
  loading.value = false
})
</script>

<style lang="scss" scoped>
.box {
  min-height: 100%;
  background-color: white;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 70rpx;

    .card,
    .card-img {
      width: 564rpx;
      height: 324rpx;
    }

    .card-type {
      font-size: 29rpx;
      font-weight: 800;
      color: #795733;
      position: absolute;
      margin-top: -220rpx;
      margin-left: 48rpx;
    }
  }

  .buddy {
    margin-top: 40rpx;

    .line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 90rpx;
      margin: 0 40rpx;
      border-bottom: 1rpx solid #f6f6f8;

      .label {
        font-size: 26rpx;
        font-weight: 400;
        color: #000000;
        line-height: 30rpx;
      }

      .value {
        font-size: 26rpx;
        font-weight: bold;
        color: #000000;
        line-height: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;

    .protocol {
      display: flex;
      flex-direction: row;
      align-items: center;

      .link {
        font-size: 24rpx;
        font-weight: 400;
        color: #3b6190;
      }

      .checkbox {
        margin-left: 30rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #03080e;
      }
    }

    .sign {
      margin-top: 20rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      width: 690rpx;
      height: 120rpx;
      padding: 20rpx 30rpx;
      background: #ffffff;
      box-shadow: 2rpx 3rpx 11rpx 0rpx rgba(0, 0, 0, 0.2);

      .price {
        display: flex;
        flex-direction: row;
        align-items: flex-end;

        .label {
          font-size: 24rpx;
          line-height: 50rpx;
          font-weight: bold;
          color: $theme-text-color-other;
        }
        .unit {
          font-size: 30rpx;
          line-height: 50rpx;
          font-weight: bold;
          color: $theme-text-color-other;
          margin-left: 10rpx;
        }

        .value {
          font-size: 48rpx;
          line-height: 60rpx;
          font-weight: bold;
          color: $theme-text-color-other;
        }
      }

      .sign-btn {
        width: 296rpx;
        height: 80rpx;
      }
    }
  }

  .close-box {
    margin-top: 80rpx;

    .close-btn {
      width: 352rpx;
      height: 82rpx;
      background: #ffffff;
      border: 2rpx solid $theme-text-color-other;
      border-radius: 40rpx;
      margin: 0 auto;
      color: $theme-text-color-other;
    }
  }
}

.arrow {
  border: solid $theme-text-color-other;
  border-width: 0 4rpx 4rpx 0;
  display: inline-block;
  padding: 4rpx;
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  margin-left: 10rpx;
}
</style>
