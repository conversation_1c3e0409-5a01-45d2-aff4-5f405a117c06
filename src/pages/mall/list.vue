<template>
  <view class="theme-bg">
    <NavBar />
    <view class="mall-top">
      <view v-if="!isLogin" class="unlogin-tips" @tap="goLogin"> 游客模式，点击登录 </view>
      <view class="mall-con">
        <view class="mall-con-item" @tap="goDetail">
          <view class="name"> {{ currentPoint }}<text>积分</text> </view>
          <view class="des"> 积分详情 </view>
        </view>
        <view hover-class="none" class="mall-con-item" @tap="goUrlPage(`/pages/my/pointTask?bus_id=${busId}`)">
          <view class="name name-min"> 赚积分 </view>
          <view class="des"> 获取更多积分 </view>
        </view>
      </view>
    </view>
    <view v-if="expPoint" hover-class="none" class="notice-wrap" @tap="goUrlPage(`/pages/my/point?bus_id=${busId}`)">
      <image class="item-img" mode="scaleToFill" src="/static/img/tongzhi.png" />
      您有{{ expPoint }}积分将于30天内到期~
    </view>
    <view v-if="isGetConfig" class="mall-main">
      <custom-tabs v-model="tabIndex" center>
        <custom-tab-pane label="积分专区">
          <GoodsList :bus-id="busId" />
        </custom-tab-pane>
        <custom-tab-pane label="商品展示">
          <GoodsList :bus-id="busId" :type="1" />
        </custom-tab-pane>
      </custom-tabs>
    </view>
    <CustomTabsBar />
    <!-- 邀请奖励弹窗 -->
    <InvitationModal :type="2" />
  </view>
</template>

<script setup lang="ts" name="class">
import InvitationModal from '@/components/InvitationModal.vue'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import GoodsList from './components/GoodsList.vue'
import CustomTabsBar from '@/components/custom-tabs-bar/index.vue'
import { usePoint } from '@/hooks/usePoint'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import { goUrlPage } from '@/utils'

const busId = ref('')
const userStore = useUserStore()
const tabIndex = ref(0)
const isLogin = ref(false)
const isGetConfig = ref(false)
const { currentPoint, getUserPointAndExpire, expPoint } = usePoint()
const themeStore = useThemeStore()
function getMallConfig() {
  themeStore.getConfig({ type: 10 }).then((res) => {
    isGetConfig.value = true
    busId.value = themeStore.theme10?.bus_id || ''
  })
}
onLoad(() => {
})
onShow(() => {
  getMallConfig()
  if (userStore.userInfoUserId) {
    isLogin.value = true
  }
  getUserPointAndExpire()
})
async function goDetail() {
  if (isLogin.value) {
    goUrlPage(`/pages/my/point?bus_id=${busId.value}`)
  } else {
    uni.navigateTo({
      url: '/pages/login?hasChecked=true&navigateBack=true',
    })
  }
}
function goLogin() {
  uni.navigateTo({
    url: '/pages/login?hasChecked=true&navigateBack=true',
  })
}
</script>
<style lang="scss" scoped>
.theme-bg {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.mall-top {
  width: 100%;
  height: 625rpx;
  background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/theme/mall-bg.png') left top no-repeat;
  background-size: cover;
  position: relative;
}
.notice-wrap {
  width: 669rpx;
  height: 50rpx;
  box-sizing: border-box;
  padding: 0 22rpx;
  line-height: 50rpx;
  display: flex;
  align-items: center;
  margin: 10rpx auto 20rpx;
  border-radius: 10rpx;
  background: rgba(255, 116, 39, 0.1);
  font-size: 22rpx;
  font-weight: bold;
  color: rgba(255, 116, 39, 1);
  .item-img {
    margin-right: 15rpx;
    width: 28rpx;
    height: 28rpx;
  }
}
.mall-main {
  flex: 1;
}
.unlogin-tips {
  position: absolute;
  right: 20rpx;
  top: 160rpx;
  font-weight: bold;
}
.mall-con {
  position: absolute;
  left: 50%;
  bottom: 10rpx;
  transform: translateX(-50%);
  width: 626rpx;
  height: 162rpx;
  display: flex;
  align-items: center;
  .mall-con-item {
    height: 110rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    font-size: 26rpx;
  }
  .name {
    font-weight: bold;
    font-size: 50rpx;
    text {
      font-size: 26rpx;
      font-weight: normal;
    }
  }
  .name-min {
    font-size: 36rpx;
    font-weight: normal;
  }
  .des {
    color: #cb2f25;
    padding-right: 15rpx;
    position: relative;
    &::after {
      content: ' ';
      position: absolute;
      right: -15rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border: 10rpx solid transparent;
      border-left: 10rpx solid #cb2f25;
    }
  }
}
[data-theme='dark'] {
  .mall-top {
    background: url('https://imagecdn.rocketbird.cn/minprogram/uni-member/theme/mall-bg-dark.png') left top no-repeat;
    background-size: cover;
  }
}
</style>
