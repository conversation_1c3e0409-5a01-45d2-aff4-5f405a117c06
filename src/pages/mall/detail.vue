<template>
  <view class="goods-box">
    <view class="box-head">
      <image
        class="goods-img"
        :src="
          goodsInfo.goods_image ||
          'https://imagecdn.rocketbird.cn/minprogram/member/image/point-store-nodata.png'
        "
        mode="heightFix"
      />
    </view>
    <view class="box-body">
      <view class="name">{{ goodsInfo.goods_name }}</view>
      <view class="price-row">
        <text v-if="goodsInfo.id" class="price-num">{{
          type == 1 ? goodsInfo.commodity_price : goodsInfo.point + '积分'
        }}</text>
        <text>库存 {{ goodsInfo.last_volume }}</text>
      </view>
      <view
        v-if="
          type == 2 &&
          (goodsInfo.barter_limit_rule_text ||
            goodsInfo.goods_type == 1 ||
            goodsInfo.goods_type == 2)
        "
        class="limit"
      >
        <text>{{ goodsInfo.barter_limit_rule_text }}</text>
        <view v-if="goodsInfo.goods_type == 1 || goodsInfo.goods_type == 2">
          <text>本人使用</text>
          <image
            class="limit-image"
            :src="'/static/img/' + (goodsInfo.use_limit_self ? 'success' : 'danger') + '.png'"
          />
          <text>转赠他人</text>
          <image
            :src="'/static/img/' + (goodsInfo.use_limit_others ? 'success' : 'danger') + '.png'"
          />
        </view>
      </view>
      <view class="desc">商品说明：{{ goodsInfo.remark || '暂无说明' }} </view>
    </view>
    <view v-if="diffPrice > 0 && type == 2" class="fixed-protocol-col">
      <view>
        您还差
        <text class="num">{{ diffPrice }}</text>
        积分可以兑换
      </view>
      <navigator hover-class="none" class="router-link" url="/pages/my/pointTask"
        >做任务赚积分</navigator
      >
    </view>
    <template v-if="goodsInfo.id">
      <view v-if="type == 1 || goodsInfo.goods_type == 5" class="fixed-bottom-wrap theme-bg">
        <button class="normal-btn" disabled>
          请联系工作人员进行{{ type == 1 ? '购买' : '兑换' }}
        </button>
      </view>
      <view v-else-if="type == 2" class="fixed-bottom-wrap theme-bg">
        <button class="normal-btn" :disabled="diffPrice > 0" @tap="goodsExchange">兑换</button>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts" name="class">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import { usePoint } from '@/hooks/usePoint'
import _ from 'lodash'
const userStore = useUserStore()
const id = ref('')
const type = ref(2) // 商品类型 1前台商品 2积分商品
const { currentPoint, getUserPoint } = usePoint()
const goodsInfo = reactive({
  id: '',
  goods_type: 1,
  use_limit: '' as any,
})
const diffPrice = computed(() => {
  return type.value == 2 ? goodsInfo.point - currentPoint.value : 0
})
onLoad((options) => {
  id.value = options.id
  type.value = options.type
})
onShow((options) => {
  getGoodsDetail()
  getUserPoint()
})
const getGoodsDetail = () => {
  http
    .get('/Pointmall/getPointGoodsInfo', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      id: id.value,
      type: type.value,
    })
    .then((res) => {
      const info = res.data.info
      let barter_limit_rule_text = ''
      // type 1前台商品 2积分商品   barter_limit 0不限制 1限制, barter_limit_rule限制规则
      if (type.value == 2 && info.barter_limit == 1 && info.barter_limit_rule) {
        const types = {
          day: '天',
          week: '周',
          month: '月',
          season: '季',
          year: '年',
        }
        info.barter_limit_rule = JSON.parse(info.barter_limit_rule.replace(/&quot;/g, '"'))
        const rule = info.barter_limit_rule
        const limit = rule.type == 1 ? '人累计' : types[Object.keys(rule.rule)[0]]
        const num = rule.type == 1 ? rule.rule : Object.values(rule.rule)[0]
        barter_limit_rule_text = `每${limit}可兑换 ${num} 件`
        console.log(barter_limit_rule_text)
      }
      Object.assign(goodsInfo, {
        ...info,
        goods_image: info.goods_image || info.goods_img,
        commodity_price: info.commodity_price
          ? '￥ ' + Number(info.commodity_price).toFixed(2)
          : '',
        use_limit_self: (info.use_limit || '').includes(1),
        use_limit_others: (info.use_limit || '').includes(2),
        barter_limit_rule_text,
      })
    })
}

const doGoodsExchange = _.throttle(
  () => {
    http
      .post('/Pointmall/goodsExchange', {
        bus_id: userStore.userInfoBusId,
        user_id: userStore.userInfoUserId,
        goods_id: id.value,
      })
      .then((res) => {
        uni.showToast({
          title: res.errormsg,
          success: () => {
            setTimeout(() => {
              // goods_type 1体验卡 2体验课 3折扣券 4金币暂定 5其他
              switch (+goodsInfo.goods_type) {
                case 1:
                case 2:
                  uni.navigateTo({
                    url: '/pages/my/card',
                  })
                  break
                case 3:
                  uni.navigateTo({
                    url: '/pages/my/coupon',
                  })
                  break
                default:
              }
            }, 1500)
          },
        })
      })
  },
  2000,
  true
)
// 处理商品兑换确认
function goodsExchange() {
  const { use_limit } = goodsInfo // 1自己可用 2转赠他人
  uni.showModal({
    title: '兑换确认',
    confirmText: '兑换',
    content: `${
      !use_limit.includes(1) && use_limit.includes(2) ? '此卡不能自用，只能转赠他人使用，' : ''
    }确认兑换吗？`,
    success: (res) => {
      if (res.confirm) {
        doGoodsExchange()
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.goods-box {
  overflow-y: auto;
  padding-bottom: 152rpx;
  height: 100vh;
  background-color: #fff;
  box-sizing: border-box;
  .box-head {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 70rpx;
    padding-bottom: 90rpx;
    background-color: #f5f7f9;
    .goods-img {
      height: 340rpx;
    }
  }

  .box-body {
    background-color: #fff;
    .name {
      margin-top: 46rpx;
      padding: 0 40rpx;
      font-size: 36rpx;
      line-height: 80rpx;
      font-weight: bold;
    }
    .price-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40rpx;
      height: 112rpx;
      font-size: 24rpx;
      .price-num {
        font-size: 30rpx;
        font-weight: bold;
        color: $theme-text-color-other;
      }
    }
    .limit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 20rpx;
      padding: 0 20rpx;
      height: 70rpx;
      font-size: 24rpx;
      background-color: #f5f7f9;
      border-radius: 10rpx;
      image {
        margin-left: 9rpx;
        width: 20rpx;
        height: 20rpx;
      }
      .limit-image {
        margin-right: 28rpx;
      }
    }
    .desc {
      margin: 22rpx 38rpx;
      padding-bottom: 40rpx;
      line-height: 48rpx;
      font-size: 24rpx;
      color: #898989;
    }
  }
  .fixed-protocol-col {
    justify-content: space-between;
    .num {
      color: $theme-text-color-other;
    }
    .router-link {
      padding-right: 30rpx;
      font-weight: bold;
      color: $theme-text-color-other;
    }
  }
  .box-foot {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .button-wrap {
      min-height: 90rpx;
      text-align: center;
      .not-exchange-text,
      .exchange-btn {
        height: 90rpx;
        line-height: 90rpx;
        font-size: 30rpx;
        font-weight: bold;
        border-radius: 0;
      }
      .not-exchange-text {
        color: #898989;
        background-color: #eee;
      }
      .exchange-btn {
        color: #ffffff;
        background-color: #ca2e53;
      }
    }
  }
}
</style>
