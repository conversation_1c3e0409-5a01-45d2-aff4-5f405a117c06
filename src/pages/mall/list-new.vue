<template>
  <view class="page-container">
    <!-- Top Points Section -->
    <view class="points-section">
      <view class="points-card">
        <view class="points-display">
          <text class="points-number">{{ currentPoint }}</text>
          <text class="points-text">积分</text>
        </view>
        <view class="points-actions">
          <view class="action-item" @tap="goDetail">
            <text class="action-text">积分详情</text>
            <text class="action-arrow">></text>
          </view>
          <view class="action-item" @tap="goUrlPage(`/pages/my/pointTask?bus_id=${busId}`)">
            <text class="action-text">获取更多积分</text>
            <text class="action-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- Notice Section -->
    <view v-if="expPoint" class="notice-section" @tap="goUrlPage(`/pages/my/point?bus_id=${busId}`)">
      <image class="notice-icon" src="/static/img/tongzhi.png" />
      <text class="notice-text">您有{{ expPoint }}积分将于24小时内到期</text>
    </view>

    <!-- Main Content Area -->
    <view class="main-content">
      <!-- Category Sidebar -->
      <view class="category-sidebar">
        <view 
          v-for="(category, index) in categories" 
          :key="index"
          class="category-item"
          :class="{ active: selectedCategory === index }"
          @tap="selectCategory(index)"
        >
          <text class="category-text">{{ category.name }}</text>
        </view>
      </view>

      <!-- Products Grid -->
      <view class="products-container">
        <view class="products-grid">
          <view 
            v-for="product in filteredProducts" 
            :key="product.id"
            class="product-item"
            @tap="goToProductDetail(product)"
          >
            <image 
              class="product-image" 
              :src="product.image" 
              mode="aspectFill"
            />
            <view class="product-info">
              <text class="product-name">{{ product.name }}</text>
              <text class="product-stock">库存 {{ product.stock }}</text>
              <view class="product-price">
                <text class="price-symbol">￥</text>
                <text class="price-number">{{ product.price }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- Bottom Tab Bar -->
    <CustomTabsBar />
    
    <!-- Invitation Modal -->
    <InvitationModal :type="2" />
  </view>
</template>

<script setup lang="ts" name="MallListNew">
import InvitationModal from '@/components/InvitationModal.vue'
import CustomTabsBar from '@/components/custom-tabs-bar/index.vue'
import { usePoint } from '@/hooks/usePoint'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import { goUrlPage } from '@/utils'

const busId = ref('')
const userStore = useUserStore()
const selectedCategory = ref(0)
const { currentPoint, getUserPointAndExpire, expPoint } = usePoint()
const themeStore = useThemeStore()

// Categories data
const categories = ref([
  { name: '全部商品', type: 'all' },
  { name: '饮料', type: 'drinks' },
  { name: '冰衣', type: 'ice-wear' },
  { name: '轻食', type: 'light-food' },
  { name: '果汁咖啡', type: 'juice-coffee' },
  { name: '积分专区', type: 'points' }
])

// Mock products data - replace with actual API data
const products = ref([
  {
    id: 1,
    name: '鲜榨橙汁',
    image: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/products/orange-juice.jpg',
    price: 500,
    stock: 10,
    category: 'drinks'
  },
  {
    id: 2,
    name: '鲜榨草莓汁',
    image: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/products/strawberry-juice.jpg',
    price: 500,
    stock: 10,
    category: 'drinks'
  },
  {
    id: 3,
    name: '儿童泳装',
    image: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/products/kids-swimwear-1.jpg',
    price: 5000,
    stock: 10,
    category: 'ice-wear'
  },
  {
    id: 4,
    name: '儿童泳装',
    image: 'https://imagecdn.rocketbird.cn/minprogram/uni-member/products/kids-swimwear-2.jpg',
    price: 5000,
    stock: 10,
    category: 'ice-wear'
  }
])

// Computed property for filtered products
const filteredProducts = computed(() => {
  if (selectedCategory.value === 0) {
    return products.value // Show all products
  }
  const categoryType = categories.value[selectedCategory.value].type
  return products.value.filter(product => product.category === categoryType)
})

// Methods
function getMallConfig() {
  themeStore.getConfig({ type: 10 }).then((res) => {
    busId.value = themeStore.theme10?.bus_id || ''
  })
}

function selectCategory(index: number) {
  selectedCategory.value = index
}

function goToProductDetail(product: any) {
  goUrlPage(`/pages/mall/detail?id=${product.id}&type=2&bus_id=${busId.value}`)
}

async function goDetail() {
  if (userStore.userInfoUserId) {
    goUrlPage(`/pages/my/point?bus_id=${busId.value}`)
  } else {
    uni.navigateTo({
      url: '/pages/login?hasChecked=true&navigateBack=true',
    })
  }
}

// Lifecycle hooks
onLoad(() => {
  // Initialize
})

onShow(() => {
  getMallConfig()
  getUserPointAndExpire()
})
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// Points Section
.points-section {
  background: #faf6f0;
  padding: 30rpx;
  position: relative;

  // Decorative diamond element
  &::after {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 30rpx;
    width: 60rpx;
    height: 60rpx;
    background: linear-gradient(45deg, #ff9a56, #ffb366);
    transform: rotate(45deg);
    border-radius: 8rpx;
    opacity: 0.8;
  }

  .points-card {
    background: transparent;
    border-radius: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .points-display {
      display: flex;
      align-items: baseline;

      .points-number {
        font-size: 120rpx;
        font-weight: bold;
        color: #333333;
        line-height: 1;
      }

      .points-text {
        font-size: 32rpx;
        color: #666666;
        margin-left: 8rpx;
        font-weight: 500;
      }
    }

    .points-actions {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      margin-top: 20rpx;

      .action-item {
        display: flex;
        align-items: center;

        .action-text {
          font-size: 28rpx;
          color: #ff7427;
          margin-right: 8rpx;
          font-weight: 500;
        }

        .action-arrow {
          font-size: 24rpx;
          color: #ff7427;
          font-weight: bold;
        }
      }
    }
  }
}

// Notice Section
.notice-section {
  margin: 20rpx 30rpx;
  background: rgba(255, 116, 39, 0.1);
  border-radius: 10rpx;
  padding: 15rpx 20rpx;
  display: flex;
  align-items: center;

  .notice-icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 15rpx;
  }

  .notice-text {
    font-size: 24rpx;
    color: #ff7427;
    font-weight: 500;
  }
}

// Main Content
.main-content {
  flex: 1;
  display: flex;
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

// Category Sidebar
.category-sidebar {
  width: 200rpx;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;

  .category-item {
    padding: 30rpx 20rpx;
    border-bottom: 1rpx solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &.active {
      background: white;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 40rpx;
        background: #ff7427;
        border-radius: 0 3rpx 3rpx 0;
      }

      .category-text {
        color: #ff7427;
        font-weight: 500;
      }
    }

    .category-text {
      font-size: 28rpx;
      color: #333333;
      text-align: center;
    }
  }
}

// Products Container
.products-container {
  flex: 1;
  padding: 30rpx;

  .products-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .product-item {
      width: 48%;
      margin-bottom: 20rpx;
      background: white;
      border-radius: 15rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

      .product-image {
        width: 100%;
        height: 200rpx;
      }

      .product-info {
        padding: 20rpx;

        .product-name {
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          display: block;
          margin-bottom: 10rpx;
        }

        .product-stock {
          font-size: 22rpx;
          color: #999999;
          display: block;
          margin-bottom: 15rpx;
        }

        .product-price {
          display: flex;
          align-items: baseline;

          .price-symbol {
            font-size: 20rpx;
            color: #ff7427;
          }

          .price-number {
            font-size: 32rpx;
            color: #ff7427;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
