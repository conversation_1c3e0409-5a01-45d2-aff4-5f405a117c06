<template>
  <view class="hastabbar">
    <view v-if="!isLogin" class="unlogin-view" @tap="goLogin"> </view>
    <view class="top-con theme-bg">
      <view class="avatar-wrap" @tap="goInfo">
        <image
          class="avatar"
          mode="aspectFill"
          :src="userInfo.avatar || 'https://imagecdn.rocketbird.cn/minprogram/uni-member/avatar.jpg'"
        />
        <view class="lef">
          <view class="username-row">
            <view class="username">
              {{ isLogin ? userInfo.nickname || '佚名' : '点击登录' }}
              <image
                v-if="!!userStore.userInfo.is_display_self_qr"
                class="qr-img"
                src="https://imagecdn.rocketbird.cn/minprogram/uni-member/my-qrcode.png"
                @tap.stop="goQrPage"
              />
            </view>
            <view class="recharge-box" @tap.stop="goRechargePage">
              <text class="label">余额</text>
              <text class="sum">{{ userInfo.balance }}</text>
              <text class="recharge-btn">充值</text>
            </view>
          </view>
          <view class="info-row">
            <view v-if="isLogin" class="info-item">
              <image class="item-img" mode="scaleToFill" src="/static/img/shouji.png" />
              {{ userInfo.phone }}
            </view>
            <view v-else class="login-tips"> 登录体验更多功能 </view>
            <view v-if="moduleInfo.is_open_member_point" class="info-item" @tap.stop="goPointDetail">
              <image class="item-img" mode="scaleToFill" src="/static/img/jifen.png" />
              {{ userInfo.point }}
            </view>
          </view>
        </view>
      </view>
      <!-- 状态1.履行中;2.扣款失败;3.合约终止;4.合约暂停 -->
      <navigator
        v-if="payscoreState == 1"
        class="payscore"
        :url="'/pages/payscore/detail?id=' + payscoreId"
        hover-class="none"
      >
        <image class="photo" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/payscore-card-bg.png" />
        <div class="message">{{ deadline }} 将{{ payscoreState == 1 ? '自动续费' : '到期' }}</div>
        <div></div>
        <div></div>
        <div class="label">管理</div>
      </navigator>
      <!-- <view class="train-info">
        <view class="train-item">
          <view class="train-top">
            <view class="num">{{ userInfo.sign_days_count }}</view
            >天
          </view>
          <view class="train-bot">训练天数</view>
        </view>
        <view class="train-item">
          <view class="train-top">
            <view class="num">{{ userInfo.sign_duration }}</view
            >分钟
          </view>
          <view class="train-bot">训练时长</view>
        </view>
        <view class="train-item">
          <view class="train-top">
            <view class="num">{{ userInfo.sign_num_count }}</view
            >次
          </view>
          <view class="train-bot">训练次数</view>
        </view>
      </view> -->
    </view>
    <navigator v-if="userInfo.reservation_count" url="/pages/my/reserveRecord" hover-class="none" class="notice-wrap">
      <image class="item-img" mode="scaleToFill" src="/static/img/tongzhi.png" />
      您有{{ userInfo.reservation_count }}节课待上
    </navigator>
    <view v-if="iconShowObj.my_interest.length" class="bot-wrap theme-bg">
      <view class="area-tit">
        <image class="area-img" mode="scaleToFill" src="/static/img/vip.png" />
        我的权益
      </view>
      <view class="nav-wrap">
        <navigator
          v-for="item in iconInfo.my_interest"
          v-show="getIsShowIcon(item.name, 'my_interest')"
          :key="item.url"
          class="bot-item bot-my"
          :url="item.url"
          hover-class="none"
        >
          <view class="icon-wrap">
            <view v-if="ticketContract !== 0 && item.name === '我的合同'" class="red-dot long-dot"
              >待签:{{ ticketContract }}</view>
            <view v-if="cardUnActiveNum !== 0 && item.name === '会员卡课'" class="red-dot long-dot"
              >未激活:{{ cardUnActiveNum }}</view>
            <ThemeIcon :size="21" :color="themeIconColor" class="item-icon" :type="item.icon" />
          </view>
          {{ item.name }}
        </navigator>
      </view>
    </view>
    <!-- 邀请有礼 -->
    <view
      v-if="themeStore.theme4.display === 1"
      class="invitation-wrap"
      @tap="goUrlPage(`/pages/invitation/detail?bus_id=${themeStore.theme4.bus_id}`)"
    >
      <image class="invitation-img" mode="widthFix" :src="themeStore.theme4.background_img" />
    </view>
    <view v-if="iconShowObj.service_item.length || moduleInfo.is_can_palmservice" class="bot-wrap theme-bg">
      <view class="area-tit">
        <image class="area-img" mode="scaleToFill" src="/static/img/fuwu.png" />
        服务项目
      </view>
      <view class="nav-wrap">
        <block v-for="item in iconInfo.service_item" :key="item.url">
          <block v-if="getIsShowIcon(item.name, 'service_item')">
            <block v-if="item.name === '在线客服'">
              <button
                class="bot-item contact-button"
                open-type="contact"
                hover-class="none"
              >
                <view class="icon-wrap">
                  <ThemeIcon :size="21" :color="themeIconColor" class="item-icon" :type="item.icon" />
                </view>
                在线客服
              </button>
            </block>
            <block v-else>
              <navigator
                class="bot-item"
                :url="item.url"
                hover-class="none"
              >
                <view class="icon-wrap">
                  <ThemeIcon :size="21" :color="themeIconColor" class="item-icon" :type="item.icon" />
                </view>
                {{ item.name }}
              </navigator>
            </block>
          </block>
        </block>
        <navigator
          v-if="moduleInfo.is_can_palmservice"
          class="bot-item"
          url="/pages/wxPay/palmManage"
          @tap="palmStaReport('my_palmservice')"
          hover-class="none"
        >
          <view class="icon-wrap">
            <ThemeIcon :size="21" :color="themeIconColor" class="item-icon" type="t-icon-wode-shuazhangfuwu" />
          </view>
          刷掌服务
        </navigator>
      </view>
    </view>
    <view v-if="iconShowObj.personal_data.length" class="bot-wrap theme-bg">
      <view class="area-tit">
        <image class="area-img" mode="scaleToFill" src="/static/img/shuju.png" />
        个人数据
      </view>
      <view class="nav-wrap">
        <navigator
          v-for="item in iconInfo.personal_data"
          v-show="getIsShowIcon(item.name, 'personal_data')"
          :key="item.url"
          class="bot-item"
          :url="item.name === '历史体测' ? item.url + numberUserId : item.url"
          hover-class="none"
        >
          <view class="icon-wrap">
            <ThemeIcon :size="21" :color="themeIconColor" class="item-icon" :type="item.icon" />
          </view>
          {{ item.name }}
        </navigator>
      </view>
    </view>
  </view>
  <CustomTabsBar />
  <!-- 邀请奖励弹窗 -->
  <InvitationModal :type="2" />
</template>

<script setup lang="ts" name="index">
import CustomTabsBar from '@/components/custom-tabs-bar/index.vue'
import InvitationModal from '@/components/InvitationModal.vue'
import http from '@/utils/request'
import { useLogin } from '@/hooks/useLogin'
import { usePalm } from '@/hooks/usePalm'
import { useThemeStore } from '@/store/theme'
import { useUserStore } from '@/store/user'
import { goUrlPage } from '@/utils'
const { checkLogin } = useLogin()
import { formatDate } from '@/utils/shared'

const numberUserId = ref('') // 这里的用户id 为未加密之前的用户id 用做体测记录页面的参数
const userInfo = reactive({
  user_id: '',
  nickname: '',
  phone: '',
  avatar: '',
  point: 0,
  ticket: 0,
  balance: 0, // 可用储值卡余额
  reservation_count: 0,
  sign_days_count: 0,
  sign_duration: 0,
  sign_num_count: 0,
})

const moduleInfo = reactive<Record<string, boolean>>({
  is_open_member_point: false,
  // is_bind_face: false,
  has_cabinet_pwd: false,
  // show_contract_menu: false,
  // is_open_invoice: false,
  // has_face_server: false,
  show_invitation_polite: false,
  // is_can_palmservice: false,
})
const iconInfo = reactive({
  my_interest: [
    { icon: 't-icon-wode-huiyuanka', name: '会员卡课', url: '/pages/my/card' },
    { icon: 't-icon-wode-ruchangpingzheng', name: '入场凭证', url: '/pages/my/ticket' },
    { icon: 't-icon-wode-yuyuejilu', name: '预约记录', url: '/pages/my/reserveRecord' },
    { icon: 't-icon-wode-zhekoujuan', name: '我的折扣券', url: '/pages/my/coupon' },
    { icon: 't-icon-wode-wodehetong', name: '我的合同', url: '/packageMy/my/contract' },
  ],
  personal_data: [
    { icon: 't-icon-wode-qiandaojilu', name: '运动记录', url: '/pages/train/trainOver' },
    { icon: 't-icon-wode-lishitice', name: '历史体测', url: '/pages/my/resultDetail?userId=' },
  ],
  service_item: [
    { icon: 't-icon-wode-wodefapiao', name: '我的发票', url: '/packageMy/my/invoiceRecord' },
    { icon: 't-icon-wode-kaiguimima', name: '开柜密码', url: '/packageMy/my/pwd' },
    { icon: 't-icon-wode-liuyanzixun', name: '留言咨询', url: '/pages/bus/feedback' },
    { icon: 't-icon-wode-renlianshibie', name: '人脸识别', url: '/packageMy/my/face' },
    { icon: 't-icon-wode-qingjiajilu2', name: '请假记录', url: '/packageMy/leave/list' },
    { icon: 't-icon-wode-zuguijilu', name: '租柜记录', url: '/packageMy/cabinet/record' },
    { icon: 't-icon-wode-wodehongbao', name: '红包记录', url: '/packageMy/redBag/log' },
    { icon: 't-icon-wode-juanmaduihuan', name: '券码兑换', url: '/packageMy/thirdParty/verify' },
    { icon: 't-icon-wode-bangzhuzhongxin', name: '帮助中心', url: '/packageMy/agreement/index' },
    { icon: 't-icon-wode-zaixiankefu', name: '在线客服', url: 'contact' },
  ],
})

const iconShowObj = reactive({
  my_interest: ['会员卡课', '入场凭证', '预约记录', '我的折扣券', '我的合同'],
  personal_data: ['运动记录', '历史体测'],
  service_item: [
    // '我的发票',
    // '开柜密码',
    // '留言咨询',
    // '人脸识别',
    // '请假记录',
    // '租柜记录',
    // '红包记录',
    // '券码兑换',
    // '帮助中心',
    // '在线客服',
  ],
})

function getIsShowIcon(name: string, type: string) {
  return iconShowObj[type].includes(name)
}

const ticketContract = ref(0)
const cardUnActiveNum = ref(0)

const isLogin = ref(false)
const userStore = useUserStore()
const themeStore = useThemeStore()
const themeIconColor = computed(() => {
  return themeStore.theme1.background_color === 2 ? '#fff' : '#000'
})

onShow(() => {
  const shouldGoBusId =
    userStore.lastMyPageBusId && userStore.userInfoBusId !== userStore.lastMyPageBusId ? userStore.lastMyPageBusId : ''
  checkLogin(false, shouldGoBusId, true).then((info) => {
    userStore.lastMyPageBusId = info.bus_id
    // 从其它页面返回到我的页面 设置为商家模式
    themeStore.changeShowMerchantMode(true)
    themeStore.getConfig({ type: 4 }).then((res) => {
      iconShowObj.my_interest = res.my_interest
      iconShowObj.personal_data = res.personal_data
      iconShowObj.service_item = res.service_item
    })
    userInfo.user_id = info.user_id || ''
    if (info.user_id) {
      isLogin.value = true
      let postData: Record<string, string> = {
        bus_id: info.bus_id,
        user_id: info.user_id,
      }
      if (themeStore.isShowMerchantMode) {
        postData = {
          m_id: info.m_id || '',
          user_id: info.user_id,
        }
      }
      getInfo(postData)
      getModuleConfigInfo(postData)
      getDeadline()
      getNotSignContractCount()
      getNotActiveCardCount()
    } else {
      isLogin.value = false
      Object.assign(userInfo, {
        nickname: '',
        phone: '',
        avatar: '',
        point: 0,
        sign_days_count: 0,
        sign_duration: 0,
        sign_num_count: 0,
      })
      uni.setStorageSync('userInfo', '')
    }
  })
})

function goInfo() {
  uni.navigateTo({
    url: '/pages/my/info',
  })
}
function goLogin() {
  uni.navigateTo({
    url: '/pages/login?hasChecked=true&navigateBack=true',
  })
}
function goQrPage() {
  uni.navigateTo({
    url: '/packageMy/my/qrCode',
  })
}
function goRechargePage() {
  uni.navigateTo({
    url: '/packageMy/recharge/index',
  })
}
function goPointDetail() {
  uni.navigateTo({
    url: '/pages/my/point',
  })
}
const getInfo = (info) => {
  http.get('Personalcenter/getIndexInfo', { user_id: info.user_id }).then((res) => {
    numberUserId.value = res.data.user_id // 明文id 未加密
    Object.assign(userInfo, {
      ...res.data,
      user_id: info.user_id,
    })
    // userStore.setUserInfo(res.data)
    uni.setStorageSync('userInfo', res.data)
  })
}

const { palmStaReport } = usePalm()
const getModuleConfigInfo = (info) => {
  http.get('Personalcenter/getModuleConfig', { user_id: info.user_id }).then((res) => {
    const info = res.data?.info || {}
    if(info.is_can_palmservice) {
      palmStaReport('my')
    }
    Object.assign(moduleInfo, info)
  })
}

const getNotSignContractCount = () => {
  return http
    .get('Personalcenter/getNotSignContractCount', {
      bus_id: '',
      user_id: userStore.userInfoUserId,
      loading: false,
    })
    .then((res) => {
      ticketContract.value = res.data.count || 0
    })
    .catch((error) => {
      ticketContract.value = 0
    })
}
const getNotActiveCardCount = () => {
  return http
    .get('Personalcenter/getNotActiveCardCount', {
      bus_id: '',
      user_id: userStore.userInfoUserId,
      loading: false,
    })
    .then((res) => {
      cardUnActiveNum.value = res.data.count || 0
    })
    .catch((error) => {
      cardUnActiveNum.value = 0
    })
}

// deadline, 状态1.履行中;2.扣款失败;3.合约终止;4.合约暂停
const payscoreId = ref('')
const payscoreState = ref('') as any
const deadline = ref('')
const getDeadline = () => {
  return http
    .post('/Contract/queryUserContract', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      loading: false,
    })
    .then((res) => {
      const timestamp = res.data?.contract?.dec_time
      const date = new Date(timestamp * 1000)
      deadline.value = formatDate(date, 'yyyy-MM-dd')
      payscoreId.value = res.data?.programme_id
      payscoreState.value = res.data?.contract?.state
    })
}
</script>

<style lang="scss">
// .page {
//   padding-bottom: 0; // tabBar页面不需要底部安全距离设置
// }
.unlogin-view {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 10;
}
.top-con {
  margin-bottom: 20rpx;
  padding: 40rpx;
}
.notice-wrap {
  display: flex;
  align-items: center;
  margin: 0 46rpx 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  background: var(--THEME-COLOR);
  font-weight: bold;
  font-size: 24rpx;
  .item-img {
    margin-right: 15rpx;
    width: 28rpx;
    height: 28rpx;
  }
}
.area-tit {
  line-height: 50rpx;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  .area-img {
    margin-right: 15rpx;
    width: 24rpx;
    height: 24rpx;
  }
}
.avatar-wrap {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 36rpx;
  .avatar {
    margin-right: 23rpx;
    border-radius: 50%;
    width: 140rpx;
    min-width: 140rpx;
    height: 140rpx;
  }

  .lef {
    width: 100%;
  }
  .username-row {
    display: flex;
    align-items: center;
    width: 100%;

    .username {
      margin-right: 16rpx;
    }
    .qr-img {
      display: inline-block;
      margin-left: 10rpx;
      width: 35rpx;
      height: 35rpx;
      vertical-align: middle;
    }

    .recharge-box {
      margin-top: 20rpx;
      margin-left: auto;
      font-size: 24rpx;
      color: $theme-text-color-other;

      .label {
        font-weight: 400;
        white-space: nowrap;
      }
      .sum {
        margin: 0 8rpx;
      }
      .recharge-btn {
        display: inline-block;
        width: 60rpx;
        height: 30rpx;
        line-height: 28rpx;
        text-align: center;
        color: #fff;
        border-radius: 15rpx;
        background: $theme-text-color-other;
      }
    }
  }

  .info-row {
    display: flex;
    align-items: center;
    margin-top: 26rpx;
    font-weight: 400;
    font-size: 24rpx;
    .item-img {
      margin-right: 8rpx;
      width: 20rpx;
      height: 20rpx;
    }
    .info-item:last-child {
      margin-left: 15rpx;
    }
  }
}
.train-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 36rpx;
  padding: 0 40rpx;
}
.train-item {
  text-align: center;
  font-size: 24rpx;
  .train-top {
    display: flex;
    justify-content: center;
    align-items: baseline;
    margin-bottom: 14rpx;
    width: 100%;
  }
  .num {
    margin-right: 4rpx;
    font-weight: bold;
    font-size: 40rpx;
  }
}
.invitation-wrap {
  margin: 0 20rpx;
  image {
    width: 100%;
  }
}
.bot-wrap {
  margin: 30rpx 20rpx 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
}
.nav-wrap {
  display: flex;
  flex-wrap: wrap;
}
.bot-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0;
  min-width: 25%;
  font-size: 22rpx;

  // Add styles for contact button
  &.contact-button {
    background: none;
    border: none;
    line-height: inherit;
    margin-bottom: 30rpx;
    font-weight: normal;
    text-align: inherit;
    color: inherit;

    &::after {
      display: none;
    }
  }

  .red-dot {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -10rpx;
    width: 30rpx;
    height: 30rpx;
    background: #ff0000;
    border-radius: 50%;
    font-size: 22rpx;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
  }
  .long-dot {
    width: auto;
    padding: 0 10rpx;
    border-radius: 6rpx;
    white-space: nowrap;
  }
  .icon-wrap {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 14rpx;
    border: 1rpx solid $theme-text-color-other;
    border-radius: 50%;
    width: 80rpx;
    height: 80rpx;
  }
}
.bot-item:last-child {
  border-bottom: 0 none;
}
.bot-my {
  flex: 1;
  min-width: 20%;
}
.style2 {
  display: block;
  .bot-item {
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f4f3f8;
    width: 100%;
  }
  .item-icon {
    margin-right: 20rpx;
  }
  .icon-wrap {
    margin-bottom: 0;
  }
}

.payscore {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  background: linear-gradient(91deg, #f6daae, #ebcaa1);
  border-radius: 20rpx;
  padding: 0 28rpx;
  margin-top: 30rpx;

  .photo {
    width: 91.4rpx;
    height: 44.8rpx;
  }

  .message {
    font-size: 24rpx;
    font-weight: bold;
    color: #000000;
  }

  .label {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 92rpx;
    height: 40rpx;
    background: #c59d6c;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #ffffff;
  }
}
</style>
