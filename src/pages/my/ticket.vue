<template>
  <custom-tabs :model-value="current" @change="tabChange">
    <template #right>
      <MerchantBusPick
        v-model="busId"
        :style="{
          display: computedId === '2' ? 'none' : 'block',
        }"
      />
    </template>
    <custom-tab-pane
      v-for="item in tabList"
      :id="item.type"
      :key="item.type"
      :label="item.label"
      :number="item.type === '1' ? canUseNumber : 0"
    >
      <view v-if="computedId !== ''" class="card-page">
        <!-- 需要提前获取总数量 使用v-show -->
        <TicketList v-if="item.type !== '2'" :type="+item.type" @update-count="handleCount" />
        <view v-if="computedId === '2'">
          <QrCode tips="请将二维码放在读取器上" :show-avatar="false" />
          <view v-if="lockerInfo.length">
            <view v-for="locker in lockerInfo" :key="locker.timestamp" style="display: flex; align-items: center; width: 700rpx; margin-left: 30rpx">
              <view class="normal-btn normal-btn-min text-ellipsis"
                @tap="handleLocker(locker)"
                >{{ locker.bus_name }} {{ locker.status === 1 ? '预选' : '已租' }}：{{ locker.cabinet_ids }}
                <!-- <view v-if="locker.status === 1" class="release-wrap" @tap.stop="releaseLockerNum(locker)">
                  <uni-icons type="close" size="20" color="#fff"></uni-icons>
                </view> -->
              </view>
              <view v-if="locker.status === 1" @tap.stop="releaseLockerNum(locker)">
                <text style="font-size: 24rpx; color: blue">释放</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import TicketList from './components/TicketList.vue'
import QrCode from '@/components/QrCode.vue'
import MerchantBusPick from '@/components/MerchantBusPick.vue'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'

const tabList = ref<Record<string, any>[]>([])
const computedId = ref('')
const { checkLogin } = useLogin()
const busId = ref('')
const current = ref(0)
const from = ref('my')
const userStore = useUserStore()
onShow(() => {
  checkLogin()
})
const canUseNumber = ref(0)
onLoad((options) => {
  from.value = options?.from || 'my'
  if (from.value === 'index') {
    tabList.value = [
      { type: '2', label: '会员凭证' },
      { type: '1', label: '可用票凭证' },
    ]
    getLockerNum()
  } else {
    tabList.value = [
      { type: '1', label: '有效凭证' },
      { type: '0', label: '失效凭证' },
    ]
  }
})
function handleCount(info) {
  if (info.type === 1) {
    canUseNumber.value = info.count
  }
}
onUnload(() => {
  // eslint-disable-next-line no-undef
  const pages = getCurrentPages()
  const page = pages[pages.length - 2]
  const path = page.route
  if (path && (path === 'pages/stadium/buySpace' || path === 'pages/stadium/buyTicket' || path === 'pages/my/ticket')) {
    uni.switchTab({
      url: '/pages/my/index',
    })
  }
})

function tabChange(e) {
  current.value = e.value
  computedId.value = e.computedId
  // if (current.value !== 0 && isRequest.value) {
  //   getInfo(computedId.value)
  // }
}
interface LockerInfo {
  bus_id: string
  user_id: string
  bus_name: string
  // cabinet_id: string
  // device_id: string
  status: number,
  cabinet_list: Array<{cabinet_id: string, cabinet_name: string}>
  cabinet_ids: string,
  timestamp: string
}
const lockerInfo = ref<LockerInfo[]>([])

function getLockerNum() {
  if (!userStore.userInfoUserId) {
    return
  }
  http
    .get('Cabinets/getBusOccupyCabinet', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      m_id: userStore.userInfoMId,
      showToast: false,
    })
    .then((res) => {
      const list = res.data || []
      lockerInfo.value = list.map((item: any) => {
        return {
          ...item,
          cabinet_ids: item.cabinet_list.map((i) => i.cabinet_id).join('、'),
          timestamp: new Date().getTime(),
        }
      })
      console.log(lockerInfo.value)
    })
    .catch((err) => {
      lockerInfo.value = []
    })
}
function releaseLockerNum(info: LockerInfo) {
  http
    .get('Cabinets/releaseUserCabinet', {
      bus_id: info.bus_id,
      user_id: info.user_id,
      // cabinet_id: info.cabinet_id,
    })
    .then((res) => {
      getLockerNum()
    })
}
function handleLocker(info: LockerInfo) {
  // if (info.status === 1) {
  //   uni.navigateTo({
  //     url: `/packageMy/cabinet/list?bus_id=${info.bus_id}&device_id=${info.device_id}`,
  //   })
  // }
  if (info.status === 2) {
    uni.navigateTo({
      url: `/packageMy/cabinet/record`,
    })
  }
}
</script>
<style lang="scss">
.card-page {
  padding-top: 20rpx;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.normal-btn-min {
  width: 500rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  &.center {
    justify-content: center;
  }
}

.text-ellipsis {
  display: inline-block;
  margin: 10rpx;
  max-width: 600rpx;
  width: 600rpx;
  white-space: nowrap;        /* 不换行 */
  overflow: hidden;           /* 超出隐藏 */
  text-overflow: ellipsis;    /* 超出显示省略号 */
}
</style>
