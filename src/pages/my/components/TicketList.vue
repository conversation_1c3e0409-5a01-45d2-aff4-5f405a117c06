<template>
  <view class="invoice-content">
    <z-paging
      ref="paging"
      v-model="dataList"
      :auto="false"
      :fixed="false"
      :show-loading-more-no-more-view="false"
      @query="loadList"
    >
      <view v-for="item in dataList" :key="item.consume_sn" class="card-item">
        <MyTicketItem :ticket="item" :is-available="type === 1" />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import MyTicketItem from './MyTicketItem.vue'
import http from '@/utils/request'
import { useMerchant } from '@/store/merchant'

const props = defineProps({
  type: {
    type: Number,
    default: 0,
  },
})
const useMerchantStore = useMerchant()
const dataList = ref<Record<string, any>[]>([])
const paging = ref()

const emits = defineEmits(['update-count'])
function loadList(pageNo, pageSize) {
  if (!useMerchantStore.userInfoUserId) {
    return paging.value && paging.value.complete([])
  }
  http
    .get('/Santicket/getVoucherList', {
      bus_id: useMerchantStore.userInfoBusId,
      user_id: useMerchantStore.userInfoUserId,
      type: props.type,
      page_no: pageNo,
      page_size: pageSize,
    })
    .then((res) => {
      paging.value.complete(res.data.list)
      emits('update-count', {
        type: props.type,
        count: res.data.count,
      })
    })
    .catch((res) => {
      paging.value.complete(false)
    })
}
watchEffect(() => {
  if (useMerchantStore.userInfoUserId) {
    loadList(1, 10)
  } else {
    paging.value && paging.value.complete([])
  }
})
</script>

<style lang="scss" scoped>
.invoice-content {
  height: 100%;
}

.card-item {
  position: relative;
  margin: 0 auto 26rpx;
  width: 691rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  .card-info {
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
  }
  .card-info-more {
    margin: 0 auto 11rpx;
    padding: 10rpx 30rpx 26rpx;
    width: 644rpx;
    border: 1px solid $theme-text-color-other;
    border-top-width: 0;
    border-radius: 0 0 4rpx 4rpx;
    box-shadow: 0px 10rpx 10rpx 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    .more-row {
      display: flex;
      margin-top: 28rpx;
      line-height: 28rpx;
      font-size: 24rpx;
      .label {
        margin-right: 36rpx;
        min-width: 124rpx;
        color: $theme-text-color-grey;
      }
      .content {
        flex: 1;
        text-align: right;
      }
    }
  }
  .status {
    top: 10rpx;
  }
  .status-des {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 20rpx;
    text-align: right;
    font-weight: bold;
    color: $theme-text-color-other;
    display: flex;
    align-items: center;
  }
  .right-btn {
    position: absolute;
    top: 126rpx;
    right: 30rpx;
    width: 130rpx;
    height: 50rpx;
    line-height: 50rpx;
    background: $theme-text-color-other;
    border-radius: 10rpx;
    margin: 0;
    font-weight: bold;
    font-size: 24rpx;
    color: #fff;
  }
}
</style>
