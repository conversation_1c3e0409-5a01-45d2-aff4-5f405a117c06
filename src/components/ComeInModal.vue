<template>
  <view>
    <uni-popup ref="msgPopup" :is-mask-click="false" @change="handleStatusChange">
      <view class="comein-modal">
        <ComeInItem :type="1" @requestSuccess="handleRequestSuccess" />
        <view class="come-btn">
          <view class="hold-text" @tap="handleHold">
            稍后处理
          </view>
          <view class="cancel-text" @tap="handleCancel">
            不再提醒
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import { useUserStore } from '@/store/user'
import ComeInItem from './ComeInItem.vue'

const emits = defineEmits(['closeModal'])

const props = defineProps({
  // 是否展示弹窗
  show: {
    type: Boolean,
  },
})

const agreeChecked = ref(false)
function checkboxChange(e) {
  agreeChecked.value = e.detail.value[0] === '1' ? true : false
}
const userStore = useUserStore()

const msgPopup = ref()

async function showPopup() {
  if (msgPopup.value) {
    msgPopup.value.open()
  } else {
    await nextTick()
    msgPopup.value.open()
  }
}

async function closePopup() {
  if (msgPopup.value) {
    msgPopup.value.close()
  } else {
    await nextTick()
    msgPopup.value.close()
  }
}

function handleHold() {
  closePopup()
}

function handleCancel() {
  http.post('Personalcenter/setUserGuidePop', {
    user_id: userStore.userInfoUserId,
    user_get_guide_pop: 1,
  }).then((res) => {
    closePopup()
  })
}

function handleRequestSuccess(info) {
  if(info.active_card || info.order_sign || info.user_face) {
    showPopup()
  }
}
// 弹窗关闭切换事件
function handleStatusChange(info) {
  if (!info.show) {
    emits('closeModal')
  }
}
</script>

<style lang="scss" scoped>
.comein-modal {
  width: 600rpx;
  padding: 50rpx 36rpx;
  background: #fff;
  border-radius: 20rpx;
  :deep(.comein-box) {
    border: 0 none;
    padding: 0;
  }
  :deep(.comein-tit) {
    font-size: 36rpx;
  }
  .checkbox-wrap {
    margin-top: 10rpx;
  }
  .come-btn {
    position: relative;
    margin-top: 20rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    .hold-text {
      font-weight: bold;
      font-size: 26rpx;
      color: #FF7427;
      line-height: 30rpx;
      text-align: center;
      margin-top: 40rpx;
    }
    .cancel-text {
      font-size: 26rpx;
      color: #7D7D7D;
      line-height: 30rpx;
      text-align: center;
      margin-top: 40rpx;
    }
  }
}
</style>
