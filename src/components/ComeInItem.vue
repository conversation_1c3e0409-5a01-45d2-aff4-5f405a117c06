<template>
  <view class="comein-box" :class="{ 'guide-wrap': type === 2 }" v-if="info.active_card || info.user_face || info.order_sign || info.user_palmservice === 0">
  <view class="comein-tit" v-if="type !== 2">入场智能提醒</view>
    <view class="comein-con">
      <view class="comein-tips" v-if="type !== 2">系统发现以下条件未满足，您可能无法顺利入场。</view>
      <view class="comein-list">
        <view class="comein-item palm-item" v-if="info.user_palmservice === 0">
          <view class="comein-item-left">
            <view class="left-tit">
              <image class="icon-palm" src="https://imagecdn.rocketbird.cn/minprogram/uni-member/wx-palm.png" />
              微信刷掌
            </view>
            <text class="left-des">推荐使用微信刷掌，刷掌过闸、存取物品</text>
          </view>
          <button class="right-btn" @tap="goPalm">
            去开通
          </button>
        </view>
        <view class="comein-item" v-if="info.active_card">
          <text>没有激活会员卡</text>
          <button class="right-btn" @tap="goCardList">
            去激活
          </button>
        </view>
         <view class="comein-item" v-if="info.user_face === 1">
          <text>没有授权门店使用人脸信息</text>
          <button class="right-btn" @tap="goFace">
            去授权
          </button>
        </view>
        <view class="comein-item" v-if="info.user_face === 2">
          <text>没有上传人脸信息</text>
          <button class="right-btn" @tap="goFace">
            去上传
          </button>
        </view>
        <view class="comein-item" v-if="info.order_sign">
          <text>有待签署合同 <text v-if="info.order_sign === 2">，请联系工作人员线下签署</text></text>
          <button class="right-btn" v-if="info.order_sign === 1" @tap="goSignList">
            去签署
          </button>
          <button class="right-btn" v-if="info.order_sign === 3 && orderSn" @tap="goSigDetail">
            去签署
          </button>
        </view>
      </view>
    </view>
  </view>
  <view class="notips-box" v-else-if="type === 3">
    新会员请查看 <text @tap="goGuide" class="link">进店指引</text>
  </view>
</template>

<script setup lang="ts">
 import { useUserStore } from '@/store/user'
 import { usePalm } from '@/hooks/usePalm'
 import http from '@/utils/request'

 const props = defineProps({
  // 只有落地页需要
  orderSn: {
    type: String,
    default: '',
  },
  orderId: {
    type: String,
    default: '',
  },
  // 页面类型 1主页 2引导页 3购卡、核销落地页
  type: {
    type: Number,
    default: 1,
  },
})
const emits = defineEmits(['requestSuccess'])


const userStore = useUserStore()
const { palmStaReport } = usePalm()

const info = reactive({
  user_palmservice: 0,
  active_card: 0,
  order_sign: 0,
  user_face: 0
})
function getInfo() {
  http.post('Personalcenter/guidePop', {
    bus_id: userStore.userInfoBusId,
    user_id: userStore.userInfoUserId,
    path_type: props.type,
    order_sn: props.orderSn || '',
  }).then((res) => {
    const resInfo = res.data
    Object.assign(info, resInfo)
    if(resInfo.user_palmservice === 0) {
      const pathAction = props.type === 1 ? 'home_popup' : props.type === 2 ? 'store_guide' : 'card_done'
      palmStaReport(pathAction)
    }
    emits('requestSuccess', info)
  })
}
getInfo();

function goPalm() {
  const clickAction = props.type === 1 ? 'home_popup_click' : props.type === 2 ? 'store_guide_click' : 'card_done_click'
  palmStaReport(clickAction)
  uni.redirectTo({
    url: `/pages/wxPay/palmManage?pathType=${props.type}`,
  })
}
function goFace() {
  uni.redirectTo({
    url: '/packageMy/my/face',
  })
}
function goGuide() {
  uni.reLaunch({
    url: '/packageMy/agreement/guide',
  })
}
function goCardList() {
  uni.reLaunch({
    url: '/pages/my/card',
  })
}
function goSignList() {
  uni.redirectTo({
    url: '/packageMy/my/contract'
  })
}
function goSigDetail() {
  uni.redirectTo({
    url: `/packageMy/my/contractDetail?orderId=${props.orderId}&isSignContract=1&bus_id=${userStore.userInfoBusId}`
  })
}
</script>
<style lang="scss" scoped>
.icon-palm {
  width: 26rpx;
  height: 26rpx;
  margin-right: 12rpx;
}
.notips-box {
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .link {
    margin-left: 8rpx;
    color: $theme-text-color-other;
  }
}
.comein-box {
  width: 100%;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  border: 2rpx solid #E7E7E7;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
  .right-btn {
    width: 144rpx;
    height: 50rpx;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    line-height: 50rpx;
    background: var(--THEME-COLOR);
    margin: 0;
    font-weight: bold;
    font-size: 26rpx;
    color: #000;
  }
}
.comein-tit {
  font-weight: bold;
  font-size: 32rpx;
  color: #000;
  line-height: 50rpx;
  text-align: center;
  margin-bottom: 16rpx;
}
.comein-tips {
  font-weight: bold;
  font-size: 24rpx;
  color: #FF462E;
  line-height: 50rpx;
  text-align: center;
}
.comein-item {
  display: flex;
  font-size: 26rpx;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  height: 126rpx;
  background: #F6F6F8;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  margin-top: 24rpx;
}

.guide-wrap {
  .comein-box {
    border: 0 none;
  }
  .right-btn {
    background: #FF7427;
    color: #fff;
  }
  .comein-item {
    border-radius: 0;
    margin: 0;
    height: 100rpx;
  }
}

.palm-item {
  background: #00A15D;
  text-align: left;
  color: #fff;
  .left-des {
    font-size: 20rpx;
  }
  .right-btn {
    background: #fff;
    color: #00A15D;
  }
  .left-tit {
    display: flex;
    align-items: center;
  }
}
</style>


